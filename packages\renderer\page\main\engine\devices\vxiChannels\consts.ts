import { BaseTableRow } from '@/renderer/components/TableTool'
import { Tree } from '@/renderer/components/TreeContent'
import { InjectionKey } from 'vue'

export type VxiChannelData = {
  name: string
  deviceList: device[]
}

export type device = {
  name?: string
  deviceName: string
  slotNum: number
  addrNum: number
}

export type CurEditVxiInfo = {
  label: string
  vxiId: number
  index?: number
  groupNodeIndex?: number
  originData?: VxiChannelData
}

export interface VxiTreeChild extends Omit<Tree<number>, 'children'> {
  originData: VxiChannelData
}

interface vxiChannelsConfig {
  devicePtr: any
  curEditVxiInfo: CurEditVxiInfo
  changeTreeNode: (id: number) => void
}

interface VxiChannelRow {
  channels: number
  type: string
  bus_status: string
  speed: string
  parity: string
  active: string
  channel_id: string
  oms_usage: string
}

export type EngVxiEditorRow = BaseTableRow<VxiChannelRow>

interface vxiInfo {
  eec: string
  channels: EngVxiEditorRow[]
  omsType: string
}

export type VxiEecData = {
  currentVxi: vxiInfo
}

export interface VxiDeviceEditorProps {
  modelValue: boolean
  deviceData?: {
    name: string
    slot: number
    address: number
    type: string
  }
}

export const contextKey: InjectionKey<vxiChannelsConfig> = Symbol('vxiChannelsConfig')
