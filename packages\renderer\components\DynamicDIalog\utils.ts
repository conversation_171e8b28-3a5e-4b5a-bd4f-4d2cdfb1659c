import { reactive, toRaw } from 'vue'
import type { DefaultValues, DialogConfig, DialogTemplate, NestedDialogContext } from './types'
import { WuiMessage } from '@wuk/wui'

const configData = reactive<Record<string, any>>({})

export const getConfigData = (
  configType: string,
  configs: Record<string, DialogConfig>,
  context?: NestedDialogContext
) => {
  const formData = configData[configType]?.formData
  const tableData = configData[configType]?.tableData
  const nestedData = configData[configType]?.nestedData
  const baseConfig = configs[configType]

  if (!baseConfig) {
    WuiMessage({
      message: `is not found type: ${configType}`,
      type: 'error',
      offset: 90
    })
    return
  }

  const defaultValues = baseConfig.defaultValues || {}

  const resolvedFormDefaults = defaultValues.form
    ? resolveDefaultValue(defaultValues.form, context)
    : {}

  const resolvedTableDefaults = defaultValues.table
    ? resolveDefaultValue(defaultValues.table, context)
    : null

  // 合并表单数据：传入数据 > 默认值 > 空对象
  const finalFormData = {
    ...createDefaultFormData(baseConfig.formTemplate?.columns || []),
    ...resolvedFormDefaults,
    ...(formData || {})
  }

  console.log('Final Form Data:', finalFormData)

  const finalTableData =
    tableData ||
    resolvedTableDefaults ||
    createDefaultTableData(baseConfig.tableTemplate?.columns || [])

  const nestedDialogs: Record<string, DialogConfig> = {}

  if (baseConfig.nestedDialogs) {
    processNestedDialogs(baseConfig, nestedDialogs)
  }

  if (typeof baseConfig.title === 'function') {
    baseConfig.title = baseConfig.title(context)
  }

  baseConfig.formTemplate?.columns.forEach(column => {
    if (column.type === 'select' && typeof column.options === 'function') {
      column.options = column.options(context)
    }
  })

  baseConfig.tableTemplate?.columns.forEach(column => {
    if (column.type === 'select' && typeof column.options === 'function') {
      column.options = column.options(context)
    }
  })

  return {
    ...baseConfig,
    form: baseConfig.formTemplate
      ? {
          data: finalFormData,
          columns: baseConfig.formTemplate.columns
        }
      : undefined,
    table: baseConfig.tableTemplate
      ? {
          data: finalTableData,
          columns: baseConfig.tableTemplate.columns
        }
      : undefined,
    nestedDialogs,
    context
  }
}

/**
 * 解析默认值，支持函数类型的动态默认值
 * @param value 默认值或返回默认值的函数
 * @param context 上下文信息
 * @returns 解析后的实际默认值
 */
export function resolveDefaultValue<T>(
  value: T | ((context?: NestedDialogContext) => T),
  context?: NestedDialogContext
): T {
  if (typeof value === 'function') {
    return (value as (context?: NestedDialogContext) => T)(context)
  }
  return value
}

/**
 * 根据列配置创建默认表单数据
 * @param columns 表单列配置
 * @returns 默认表单数据对象
 */
export function createDefaultFormData(columns: any[]): Record<string, any> {
  const defaultData: Record<string, any> = {}
  columns.forEach(column => {
    if (column.type !== 'button') {
      defaultData[column.name] = ''
    }
  })

  return defaultData
}

/**
 * 根据列配置创建默认表格数据
 * @param columns 表格列配置
 * @returns 包含一行默认数据的数组
 */
export function createDefaultTableData(columns: any[]): any[] {
  const defaultData = [createDefaultRowData(columns)]
  return defaultData
}

/**
 * 根据列配置创建默认行数据
 * @param columns 表格列配置
 * @returns 默认行数据对象
 */
export function createDefaultRowData(columns: any[]): Record<string, any> {
  const defaultRow: Record<string, any> = {
    flag: false,
    row_type: '*'
  }
  columns.forEach(column => {
    defaultRow[column.name] = ''
  })
  return defaultRow
}

/**
 * 获取配置的默认值
 * @param configType 配置类型
 * @param configs 配置对象
 * @returns 默认值配置，可能包含函数类型的动态默认值
 */
export function getConfigDefaultValues(
  configType: string,
  configs: Record<string, any>
): DefaultValues | null {
  const baseConfig = configs[configType]
  return baseConfig?.defaultValues || null
}

/**
 * 解析并获取配置的实际默认值（执行函数类型的默认值）
 * @param configType 配置类型
 * @param configs 配置对象
 * @param context 上下文信息
 * @returns 解析后的实际默认值
 */
export function getResolvedConfigDefaultValues(
  configType: string,
  configs: Record<string, any>,
  context?: NestedDialogContext
): { form?: Record<string, any>; table?: any[]; nestedDialogs?: Record<string, any> } | null {
  const defaultValues = getConfigDefaultValues(configType, configs)
  if (!defaultValues) return null

  const resolved: any = {}

  if (defaultValues.form) {
    resolved.form = resolveDefaultValue(defaultValues.form, context)
  }

  if (defaultValues.table) {
    resolved.table = resolveDefaultValue(defaultValues.table, context)
  }

  if (defaultValues.nestedDialogs) {
    resolved.nestedDialogs = defaultValues.nestedDialogs
  }

  return resolved
}

/**
 * 解析嵌套弹窗的默认值（在嵌套弹窗打开时调用）
 * @param nestedDefaults 嵌套弹窗的默认值配置
 * @param context 上下文信息
 * @returns 解析后的嵌套弹窗默认值
 */
export function getResolvedNestedDefaultValues(
  nestedDefaults: any,
  context?: NestedDialogContext
): { form?: Record<string, any>; table?: any[] } {
  const resolved: any = {}

  if (nestedDefaults.form) {
    resolved.form = resolveDefaultValue(nestedDefaults.form, context)
  }

  if (nestedDefaults.table) {
    resolved.table = resolveDefaultValue(nestedDefaults.table, context)
  }

  return resolved
}

function processNestedDialogs(config: DialogConfig, target: Record<string, DialogConfig>) {
  if (config.nestedDialogs) {
    Object.entries(config.nestedDialogs).forEach(([key, nestedConfig]) => {
      target[key] = {
        ...nestedConfig,
        formTemplate: nestedConfig.formTemplate,
        tableTemplate: nestedConfig.tableTemplate
      }

      // 递归处理嵌套的 nestedDialogs
      if (nestedConfig.nestedDialogs) {
        target[key].nestedDialogs = {}
        processNestedDialogs(nestedConfig, target[key].nestedDialogs)
      }
    })
  }
}

/**
 * 通用数据处理函数：处理动态弹窗的表单和表格数据
 * @param formData 表单数据
 * @param tableData 表格数据
 * @returns 处理后的数据对象
 */
export const processDialogData = (formData: any, tableData: any[] = []) => {
  // 处理表单数据：使用 toRaw 去除响应式
  const processedFormData = formData ? toRaw(formData) : {}

  // 处理表格数据：去除 flag 和 row_type 字段
  const processedTableData = Array.isArray(tableData)
    ? tableData.map(item => {
        if (item && typeof item === 'object') {
          const { flag, row_type, ...rest } = item
          return rest
        }
        return item
      })
    : []

  return {
    formData: processedFormData,
    tableData: processedTableData
  }
}
