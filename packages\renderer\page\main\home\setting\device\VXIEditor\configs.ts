import type { DialogConfig, NestedDialogContext } from '@/renderer/components/DynamicDIalog/types'

// CONDOR_429 设备的静态配置
export const CONDOR_429: Omit<DialogConfig, 'form' | 'table'> = {
  title: (context?: NestedDialogContext) => {
    const mainData = context?.mainData
    const currentDevice = context?.currentDevice
    return `${currentDevice?.name} Device Editor - Slot: ${mainData?.slot} Address: ${mainData?.card_address} EEC: ALL`
  },
  width: '900px',
  key: 'CONDOR_429',
  formTemplate: {
    columns: [
      {
        type: 'input',
        name: 'ctd',
        label: 'Clock Tic Duration(micro sec)',
        placeholder: 'Please Input',
        labelWidth: '210'
      }
    ]
  },
  defaultValues: {
    form: (context?: NestedDialogContext) => {
      return {
        ctd: context?.mainData?.cfg?.form?.ctd
      }
    },
    table: (context?: NestedDialogContext) => {
      return context?.mainData?.cfg?.table || []
    }
  },
  tableTemplate: {
    columns: [
      {
        label: 'Channels',
        name: 'channels',
        type: 'input'
      },
      {
        label: 'Type',
        name: 'type',
        type: 'select',
        options: (context?: NestedDialogContext) => {
          return [
            { label: 'Type1', value: 'Type1' },
            { label: 'Type2', value: 'Type2' }
          ]
        }
      },
      {
        label: 'Bus Status',
        name: 'bus_status',
        type: 'input'
      },
      {
        label: 'Speed',
        name: 'speed',
        type: 'select',
        options: [
          { label: 'FAST', value: 'FAST' },
          { label: 'SLOW', value: 'SLOW' }
        ]
      },
      {
        label: 'Parity',
        name: 'parity',
        type: 'select',
        options: [
          { label: 'NONE', value: 'NONE' },
          { label: 'ODD', value: 'ODD' },
          { label: 'EVEN', value: 'EVEN' }
        ]
      },
      {
        label: 'Active',
        name: 'active',
        type: 'select',
        options: [
          { label: 'INACTIVE', value: '0' },
          { label: 'ACTIVE', value: '1' }
        ]
      },
      {
        label: 'Channel ID',
        name: 'channel_id',
        type: 'select',
        options: [
          { label: 'NONE', value: 'NONE' },
          { label: 'A', value: 'A' },
          { label: 'B', value: 'B' }
        ]
      }
    ]
  }
}

// CENCO_TACH 设备的动态配置
// 支持通过 context.mainData.cfg 传入动态配置数据，如果没有传入则使用默认值
//
// 动态配置数据结构示例：
// {
//   calibModeOptions: [{ label: 'Custom1', value: 'Custom1' }, { label: 'Custom2', value: 'Custom2' }],
//   table: [...], // 直接传入表格数据
//   channelCount: 10, // 默认通道数量
//   defaultCalibMode: 'None', // 默认校准模式
//   signalEditor: {
//     formData: { conversionType: 'polynomial', ... }, // 直接传入表单数据
//     defaultSignalRange: { minimum: '0', maximum: '100', units: 'Hz' },
//     defaultScaleRange: { minimum: '0', maximum: '100', units: 'Hz' },
//     defaultConversionType: 'polynomial'
//   },
//   polynomialEditor: {
//     formData: { coefficient0: 500, ... }, // 直接传入表单数据
//     defaultCoefficient0: 500
//   }
// }
export const CENCO_TACH: Omit<DialogConfig, 'form' | 'table'> = {
  title: (context?: NestedDialogContext) => {
    const mainData = context?.mainData
    const currentDevice = context?.currentDevice
    return `${currentDevice?.name} CENCO_TACH Device Editor - Slot: ${mainData?.slot} Address: ${mainData?.address}`
  },
  key: 'CENCO_TACH',
  tableTemplate: {
    columns: [
      {
        label: 'Channel',
        name: 'channel',
        type: 'input',
        width: '100px'
      },
      {
        label: 'Signal Name',
        name: 'signal_name',
        type: 'input'
      },
      {
        label: 'Calib Mode',
        name: 'calib_mode',
        type: 'select',
        options: (context?: NestedDialogContext) => {
          // 如果上层传入了动态配置数据，使用动态数据，否则使用默认值
          const dynamicOptions = context?.mainData?.cfg?.form.calibModeOptions
          return (
            dynamicOptions || [
              { label: 'VerifyOnly', value: 'VerifyOnly' },
              { label: 'None', value: 'None' }
            ]
          )
        }
      }
    ]
  },
  defaultValues: {
    table: (context?: NestedDialogContext) => {
      const currentDevice = context?.currentDevice
      const mainData = context?.mainData

      // 如果上层传入了动态配置数据，使用动态数据
      if (context?.mainData?.cfg?.table) {
        return context.mainData.cfg.table
      }

      // 否则使用默认生成逻辑
      const channelCount = context?.mainData?.cfg?.form?.channelCount || 7
      const defaultCalibMode = context?.mainData?.cfg?.form?.defaultCalibMode || 'VerifyOnly'

      const result = Array.from({ length: channelCount }, (_, index) => {
        const number = index.toString().padStart(3, '0')
        return {
          channel: index,
          signal_name: `${currentDevice?.name || ''}_TACH${mainData?.address || ''}_${number}`,
          calib_mode: defaultCalibMode
        }
      })
      return result
    }
  },
  nestedDialogs: {
    signalEditor: {
      title: (context?: NestedDialogContext) => {
        const currentDevice = context?.currentDevice
        const mainData = context?.mainData
        const index = context?.selectedRowIndex || 0
        const number = index.toString().padStart(3, '0')
        return `${currentDevice.name}_TACH${mainData?.address}_${number} Signal Editor`
      },
      key: 'CENCO_TACH_1',
      defaultValues: {
        form: (context?: NestedDialogContext) => {
          const mainData = context?.mainData
          const channel = context?.selectedRow?.channel

          // 如果上层传入了动态配置数据，使用动态数据
          const dynamicFormData = context?.mainData?.cfg?.form?.signalEditor?.formData
          if (dynamicFormData) {
            return {
              source: `Slot ${mainData?.slot},Address ${mainData?.address} CENCO_TACH Channel ${channel}, Frequency`,
              ...dynamicFormData
            }
          }

          // 否则使用默认值
          const defaultSignalRange = context?.mainData?.cfg?.form?.signalEditor
            ?.defaultSignalRange || {
            minimum: '22',
            maximum: '33',
            units: '44'
          }
          const defaultScaleRange = context?.mainData?.cfg?.form?.signalEditor
            ?.defaultScaleRange || {
            minimum: '22',
            maximum: '33',
            units: '44'
          }
          const defaultConversionType =
            context?.mainData?.cfg?.form?.signalEditor?.defaultConversionType || 'none'

          return {
            source: `Slot ${mainData?.slot},Address ${mainData?.address} CENCO_TACH Channel ${channel}, Frequency`,
            conversionType: defaultConversionType,
            signal_range: defaultSignalRange,
            scale_range: defaultScaleRange,
            signal_defaults: 'Signal Defaults',
            measurement: {
              select: '',
              input: ''
            }
          }
        }
      },
      formTemplate: {
        columns: [
          {
            type: 'span',
            name: 'source',
            label: 'source'
          },
          {
            type: 'range',
            label: 'Signal Range',
            name: 'signal_range',
            labelWidth: '100',
            class: 'full-width range-form-item'
          },
          {
            type: 'range',
            label: 'Scale Range',
            name: 'scale_range',
            labelWidth: '100',
            showLabel: false,
            class: 'full-width range-form-item'
          },
          {
            type: 'select',
            label: 'EU Conversion',
            name: 'conversionType',
            options: [
              { label: 'None', value: 'none' },
              { label: 'Polynomial', value: 'polynomial' }
            ]
          },
          {
            type: 'button',
            label: 'Edit EU Conversion',
            buttonType: 'primary',
            labelWidth: '150',
            dynamicNestedDialogs: {
              dependsOn: 'conversionType',
              mapping: {
                polynomial: 'polynomialEditor'
              }
            }
          },
          {
            type: 'span',
            name: 'signal_defaults',
            class: 'full-width',
            style: 'padding-left:40%'
          },
          {
            type: 'select',
            label: 'Low Pass Filter(kHz)',
            name: 'low_pass_filter',
            labelWidth: '150',
            options: [
              { label: '100', value: '100' },
              { label: '10', value: '10' },
              { label: '2', value: '2' },
              { label: '1', value: '1' }
            ]
          },
          {
            label: 'Sensitivity(VDC)',
            name: 'sensitivity',
            labelWidth: '120',
            type: 'input'
          },
          {
            type: 'select-input',
            label: 'Measurement',
            name: 'measurement',
            options: [
              { label: 'Constant Terminal Event Count', value: '0' },
              { label: 'Auto Ranged Teiminal Event Count', value: '1' },
              { label: 'Aperature Windows', value: '2' }
            ]
          },
          {
            type: 'input',
            label: 'Comments',
            name: 'comments',
            isArea: true,
            class: 'full-width'
          }
        ]
      },
      nestedDialogs: {
        polynomialEditor: {
          title: 'Polynomial Editor111',
          key: 'CENCO_TACH_2',
          formTemplate: {
            columns: [
              {
                type: 'input',
                name: 'coefficient0',
                label: 'Coefficient 0',
                placeholder: 'Enter coefficient 0'
              }
            ]
          },
          defaultValues: {
            form: (context?: NestedDialogContext) => {
              // 如果上层传入了动态配置数据，使用动态数据
              const dynamicPolynomialData = context?.mainData?.cfg?.form?.polynomialEditor?.formData
              if (dynamicPolynomialData) {
                return dynamicPolynomialData
              }

              // 否则使用默认值
              const defaultCoefficient0 =
                context?.mainData?.cfg?.form?.polynomialEditor?.defaultCoefficient0 || 250
              return {
                coefficient0: defaultCoefficient0
              }
            }
          }
        }
      }
    }
  }
}

export const DEVICE_CONFIGS = {
  CONDOR_429: CONDOR_429,
  CENCO_TACH: CENCO_TACH
  // 其他设备类型...
} as const
