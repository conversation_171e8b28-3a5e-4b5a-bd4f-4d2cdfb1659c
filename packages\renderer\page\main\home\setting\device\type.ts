import { BaseTableRow } from '@/renderer/components/TableTool'
import { printerType, VxiCard } from '@wuk/cfg'

export type SetupDeviceRow = BaseTableRow<VxiCard>

import type { DialogConfig, NestedDialogContext } from '@/renderer/components/DynamicDIalog/types'

export const PRINTER: Omit<DialogConfig, 'form' | 'table'> = {
  title: (context?: NestedDialogContext) => {
    return `EditDevice`
  },
  width: '400px',
  key: 'PRINTER',
  formTemplate: {
    columns: [
      {
        label: 'Printer Type',
        name: 'printer_type',
        type: 'select',
        options: (context?: NestedDialogContext) => {
          return Object.entries(printerType).map(([key, value]) => ({
            label: value,
            value: key
          }))
        }
      }
    ]
  },
  defaultValues: {
    form: (context?: NestedDialogContext) => {
      return {
        printer_type: context?.mainData?.info.printer_type || 'EPSON'
      }
    }
  }
}

export const DEVICE_TYPES = {
  PRINTER: PRINTER
  // 其他设备类型...
} as const
