// 表单字段类型
export interface FormFieldOption {
  label: string
  value: string | number
}

export interface BaseFormField {
  name?: string
  label?: string
  placeholder?: string
  style?: string
  class?: string
  required?: boolean
  disabled?: boolean
  labelWidth?: string
}

type FunctionOrValue<T, Context = any> = T | ((context?: Context) => T)

export interface InputFormField extends BaseFormField {
  type: 'input'
  inputType?: 'text' | 'number' | 'password'
  isArea?: boolean // 是否是区域显示
}

export interface SelectFormField extends BaseFormField {
  type: 'select'
  options: FunctionOrValue<FormFieldOption[], NestedDialogContext>
  multiple?: boolean
}

export interface DateFormField extends BaseFormField {
  type: 'date'
  format?: string
}

export interface RadioFormField extends BaseFormField {
  type: 'radio'
  options: FunctionOrValue<FormFieldOption[], NestedDialogContext>
}

export interface ButtonFormField extends BaseFormField {
  type: 'button'
  nestedDialogKey?: string // 要打开的嵌套弹窗的key
  buttonType?: 'primary' | 'default' | 'success' | 'warning' | 'danger'
  // 动态弹窗配置 - 根据其他字段的值来决定打开哪个弹窗
  dynamicNestedDialogs?: {
    dependsOn: string // 依赖的字段名称（通常是select字段）
    mapping: Record<string, string> // 值到弹窗key的映射
  }
}

export interface SpanFormField extends BaseFormField {
  type: 'span'
  isNotLine?: boolean // 是否不换行
}

export interface RangeFormField extends Omit<BaseFormField, 'placeholder'> {
  type: 'range'
  unitOptions?: Array<{ label: string; value: string }>
  placeholder?: {
    minimum?: string
    maximum?: string
    units?: string
  }
  showLabel?: boolean
}

export interface colorSelectFormField extends BaseFormField {
  type: 'colorSelect'
}

export interface matrixFormField extends BaseFormField {
  type: 'matrix'
}

export interface SelectInputFormField extends BaseFormField {
  type: 'select-input'
  options: FunctionOrValue<FormFieldOption[], NestedDialogContext>
  inputType?: 'text' | 'number' | 'password'
  isArea?: boolean // 是否是区域显示
}

export type FormField =
  | InputFormField
  | SelectFormField
  | DateFormField
  | RadioFormField
  | ButtonFormField
  | SpanFormField
  | RangeFormField
  | colorSelectFormField
  | matrixFormField
  | SelectInputFormField

// 表格列类型
export interface BaseTableColumn {
  name: string
  label: string
  width?: string | number
  align?: 'left' | 'center' | 'right'
}

export interface InputTableColumn extends BaseTableColumn {
  type: 'input'
  inputType?: 'text' | 'number'
}

export interface SelectTableColumn extends BaseTableColumn {
  type: 'select'
  options: FunctionOrValue<FormFieldOption[], NestedDialogContext>
}

export type TableColumn = InputTableColumn | SelectTableColumn

export interface DefaultValues {
  form?: Record<string, any> | ((context?: NestedDialogContext) => Record<string, any>)
  table?: any[] | ((context?: NestedDialogContext) => any[])
  nestedDialogs?: Record<string, DefaultValues>
}

// 表格行数据
export interface TableRowData {
  [key: string]: any
  flag?: boolean // 编辑状态
  row_type?: 'add' | 'insert' | 'edit' | '*' // 行类型
}

// 弹窗配置模板（静态配置）
export interface DialogTemplate {
  title?: string | ((context?: NestedDialogContext) => string)
  width?: string
  height?: string
  key: string
  defaultValues?: DefaultValues
  formTemplate?: {
    columns: FormField[]
    rules?: Record<string, any[]>
  }
  tableTemplate?: {
    columns: TableColumn[]
    height?: string | number
    showOperation?: boolean
    operations?: string[] // 支持的操作类型
  }
  // 嵌套弹窗模板
  nestedDialogs?: Record<string, DialogTemplate>
}

// 弹窗配置（包含数据）
export interface DialogConfig extends DialogTemplate {
  title?: string | ((context?: NestedDialogContext) => string)
  form?: {
    data: Record<string, any>
    columns: FormField[]
    rules?: Record<string, any[]>
  }
  table?: {
    data: TableRowData[]
    columns: TableColumn[]
    height?: string | number
    showOperation?: boolean
    operations?: string[] // 支持的操作类型
  }
  // 嵌套弹窗配置
  nestedDialogs?: Record<string, DialogConfig>
  // 自定义上下文数据
  context?: Record<string, any>
}

// 嵌套路径节点
export interface NestedPathNode {
  dialogKey: string
  formData?: Record<string, any>
  tableData?: TableRowData[]
  selectedRowIndex?: number
  selectedRow?: TableRowData
}

// 嵌套弹窗上下文
export interface NestedDialogContext {
  // 嵌套路径：从根弹窗到当前弹窗的完整路径
  nestedPath?: NestedPathNode[]
  // 当前弹窗信息
  currentDialog?: {
    dialogKey: string
    level: number // 嵌套层级，0为根弹窗
    currentRowIndex?: number
    currentRow?: TableRowData
  }
  // 便捷访问：直接父级信息
  parentFormData?: Record<string, any>
  key?: string
  parentTableData?: TableRowData[]
  selectedRow?: TableRowData
  selectedRowIndex?: number
  [key: string]: any // 允许扩展其他上下文数据
}

// API 操作类型
export type ApiOperation = 'create' | 'update' | 'delete' | 'query'

// API 回调函数类型
export interface ApiCallbacks {
  // 表单提交
  onFormSubmit?: (formData: Record<string, any>, context?: NestedDialogContext) => Promise<boolean>

  // 表格行操作 - 局部更新模式
  onTableRowAdd?: (rowData: TableRowData, context?: NestedDialogContext) => Promise<boolean>
  onTableRowUpdate?: (
    rowData: TableRowData,
    index: number,
    context?: NestedDialogContext
  ) => Promise<boolean>
  onTableRowDelete?: (index: number, context?: NestedDialogContext) => Promise<boolean>

  // 整体数据更新 - 不需要嵌套index，直接修改所有数据
  onDataUpdate?: (
    allData: {
      formData: Record<string, any>
      tableData: TableRowData[]
    },
    context?: NestedDialogContext
  ) => Promise<boolean>

  // 整体数据保存 - 保存当前弹窗的所有数据
  onDataSave?: (
    allData: {
      formData: Record<string, any>
      tableData: TableRowData[]
    },
    context?: NestedDialogContext
  ) => Promise<boolean>

  // 嵌套路径局部更新 - 支持多层嵌套的精确定位更新
  onNestedUpdate?: (
    updateData: {
      type: 'form' | 'tableRow' | 'tableAdd' | 'tableDelete'
      data?: any
      index?: number
    },
    nestedPath: NestedPathNode[],
    context?: NestedDialogContext
  ) => Promise<boolean>

  // 大接口模式 - 一次性处理所有操作
  onBulkOperation?: (
    operation: {
      type: 'save' | 'update' | 'delete'
      allDialogData: {
        [dialogKey: string]: {
          formData: Record<string, any>
          tableData: TableRowData[]
          level: number
        }
      }
    },
    context?: NestedDialogContext
  ) => Promise<boolean>
}

// 弹窗事件
export interface DialogEvents {
  onOk?: () => void
  onCancel?: () => void
  onFormChange?: (formData: Record<string, any>) => void
  onTableChange?: (tableData: TableRowData[]) => void
}

// 组件 Props
export interface DynamicDialogProps {
  visible: boolean
  config: DialogConfig
  apiCallbacks?: ApiCallbacks
  events?: DialogEvents
}

// 嵌套弹窗管理器
export interface NestedDialogManager {
  openDialog: (key: string, config?: DialogConfig) => void
  closeDialog: (key: string) => void
  isDialogOpen: (key: string) => boolean
  getDialogConfig: (key: string) => DialogConfig | undefined
}
