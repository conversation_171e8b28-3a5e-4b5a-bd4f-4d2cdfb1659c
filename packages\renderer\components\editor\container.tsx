import { defineComponent, nextTick, onMounted, onUnmounted, ref } from 'vue'
import { isArrayEqua<PERSON>, keyChecker, prefix } from './utils'

import Guides from 'vue3-guides'
import InfiniteViewer from 'vue3-infinite-viewer'
import Moveable from 'vue3-moveable'
import Selecto from 'vue3-selecto'

import { useBizEngine } from '@/renderer/hooks'
import {
  useStoreRoot,
  useStoreState,
  useStoreStateSetPromise,
  useStoreStateValue,
  useStoreValue
} from '@/renderer/store'
import { toRefs } from '@vueuse/core'
import { DisplayObjectType, UDisplay, UObject } from '@wuk/cfg'
import { WuiMessage } from '@wuk/wui'
import { Panel, PanelGroup, PanelResizeHandle } from 'vue-resizable-panels'
import { useDrop } from 'vue3-dnd'
import { isMacintosh } from './consts'
import { checkAttrs, components, DragBoxItem, ItemTypes, schemaTypes } from './controls'
import { BaseDefineType } from './controls/types'
import './index.scss'
import { ComponentPanel, TopView } from './layouts'
import { PropertyPanel } from './layouts/propertyPanel'
import { Editor } from './states'
import { LayerGroupProps, registerHistoryTypes } from './states/histories/histories'
import { createLayer, getNextId } from './states/layers'
import {
  $alt,
  $currentScena,
  $darkMode,
  $editor,
  $fileIndex,
  $horizontalGuides,
  $infiniteViewer,
  $isMove,
  $layers,
  $meta,
  $moveable,
  $selectedLayers,
  $selectedMenu,
  $selecto,
  $shift,
  $showGuides,
  $space,
  $verticalGuides,
  $zoom,
  clearStates
} from './stores'
import {
  EditorInstance,
  ScenaElementInfo,
  ScenaElementLayer,
  ScenaElementLayerGroup,
  ViewElement,
  ViewFrameProperty
} from './types'
import { GuidesView, InfiniteView, MoveableView, SelectoView, ViewNode, Viewport } from './view'

export const Container = defineComponent({
  name: 'Container',
  setup(props, { expose }) {
    const root = useStoreRoot()
    const showRightPanel = ref(false)
    const editorRef = new ViewElement<EditorInstance | undefined>(undefined)
    const dropRef = ref<HTMLDivElement | null>(null)
    const viewportRef = ref<HTMLDivElement | undefined>()
    const bizEngine = useBizEngine()

    const editor = Editor.createEditor(root, editorRef)
    const horizontalGuidesRef = ref<Guides>()
    const verticalGuidesRef = ref<Guides>()
    const infiniteViewerRef = ref<InfiniteViewer>()
    const selectoRef = ref<Selecto>()
    const moveableRef = ref<Moveable>()

    useStoreValue($moveable, moveableRef)
    useStoreValue($selecto, selectoRef)
    useStoreValue($infiniteViewer, infiniteViewerRef)
    useStoreValue($horizontalGuides, horizontalGuidesRef)
    useStoreValue($verticalGuides, verticalGuidesRef)
    useStoreValue($selectedMenu, editor.menu.selected)
    useStoreValue($isMove, editor.menu.isMove)
    useStoreValue($zoom, editor.state.zoom)
    useStoreValue($currentScena, editor.info?.option)

    const [currentScena, setCurrentScena] = useStoreState($currentScena)
    const [selectedMenu, setSelectedMenu] = useStoreState($selectedMenu)
    const [isMove, setIsMove] = useStoreState($isMove)
    const [zoom, setZoom] = useStoreState($zoom)
    const showGuides = useStoreStateValue($showGuides)
    const darkMode = useStoreStateValue($darkMode)
    const isSpace = useStoreStateValue($space)
    const [fileIndex, setFileIndex] = useStoreState($fileIndex)

    const layers = useStoreStateValue($layers)
    const setLayersPromise = useStoreStateSetPromise($layers)
    const selectedLayersStore = useStoreValue($selectedLayers)
    const setSelectedLayersPromise = useStoreStateSetPromise($selectedLayers)
    const setLayers = (
      layers: ScenaElementLayer[],
      groups = editor.layers.groups,
      selectedLayerGroups?: Array<ScenaElementLayer | ScenaElementLayerGroup>,
      isRestore?: boolean
    ) => {
      const prevs =
        (!isRestore && {
          layers: editor.layers.layers,
          groups: editor.layers.groups.slice(),
          selects: (selectedLayerGroups && selectedLayersStore.value) || undefined
        }) ||
        undefined
      editor.layers.setLayers(layers, groups)
      return setLayersPromise(layers).then(async complete => {
        if (selectedLayerGroups !== undefined) {
          await setSelectedLayers(selectedLayerGroups, true)
        } else {
          editor.layers.calculateLayers()
        }
        if (!isRestore) {
          const nexts: LayerGroupProps = {
            layers: editor.layers.layers,
            groups: editor.layers.groups,
            selects: selectedLayerGroups
          }
          editor.historys.addHistory('layer', { prevs, nexts }, 'Change Layers')
        }

        return complete
      })
    }
    const removeLayers = async (
      layerGroups: Array<ScenaElementLayer | ScenaElementLayerGroup>,
      isRestore?: boolean
    ) => {
      if (!layerGroups.length) {
        return false
      }
      const delTasks: Record<string, boolean> = {}
      layerGroups.forEach(it => (delTasks[it.id] = true))

      const selectedLayers = selectedLayersStore.value?.filter(it => !delTasks[it.id])
      await setSelectedLayers(selectedLayers, isRestore)

      const nextLayers = editor.layers.layers.filter(it => !delTasks[it.id])
      const nextGroups = editor.layers.groups.filter(it => !delTasks[it.id])

      return setLayers(nextLayers, nextGroups)
    }

    const removeSelectedLayers = () => {
      const selectedLayers = selectedLayersStore.value
      return removeLayers(selectedLayers, true)
    }

    const setSelectedLayers = (
      nextLayers: Array<ScenaElementLayer | ScenaElementLayerGroup>,
      isRestore?: boolean
    ) => {
      if (isMove.value) {
        nextLayers = []
      }

      const prevLayers = selectedLayersStore.value
      if (isArrayEquals(prevLayers, nextLayers)) {
        return Promise.resolve(false)
      }
      return setSelectedLayersPromise(nextLayers).then(complete => {
        if (!complete) {
          return false
        }
        editor.layers.calculateLayers()

        if (!isRestore) {
          const prevs = prevLayers
          const nexts = nextLayers

          editor.historys.addHistory('selectTargets', { prevs, nexts })
        }

        selectoRef.value?.setSelectedTargets(editor.layers.toTargetList(nextLayers).flatten())
        editor.actions.act('set.selected.layers')

        return true
      })
    }

    const setProperty = async <T extends BaseDefineType, V = any>(
      layer: ScenaElementLayer,
      cfg: T,
      val: V
    ) => {
      const key = cfg.key
      const fields = layer.schema?.fields || {}
      const meta = layer.meta
      const last = meta[key]
      console.info(
        'setProperty=====',
        layer.title || layer.id,
        key,
        fields[key],
        last,
        JSON.stringify(val)
      )

      if (fields[key] && last !== val) {
        meta[key] = val

        if (cfg.belong !== 'base') {
          const attr = await checkAttrs(fields, { [key]: val })
          layer.view?.value?.refresh(attr)
        } else {
          layer.frame.value?.refresh(meta)
          moveableRef.value?.updateRect()
        }
      }
    }

    const setFrameProperties = async (
      layer: ScenaElementLayer,
      attrs: Partial<ViewFrameProperty>,
      isRefresh = true
    ) => {
      attrs = JSON.parse(JSON.stringify(attrs))
      await checkAttrs(layer.schema?.fields || {}, attrs)
      if (Object.keys(attrs).length === 0) {
        return
      }
      const meta = layer.meta
      Object.assign(meta, attrs)

      console.info('setFrameProperties=====', layer.title || layer.id, attrs, isRefresh)

      if (isRefresh) {
        layer.frame.value?.refresh(meta)
        moveableRef.value?.updateRect()
      }
    }

    const setViewProperties = async (
      layer: ScenaElementLayer,
      attrs: Record<string, any>,
      isRefresh = true
    ) => {
      attrs = JSON.parse(JSON.stringify(attrs))

      const val = await checkAttrs(layer.schema?.fields || {}, attrs)
      if (Object.keys(attrs).length === 0) {
        return
      }
      Object.assign(layer.meta, attrs)

      if (isRefresh && Object.keys(val).length > 0) {
        layer.view?.value?.refresh(val)
      }
    }

    const getSelectedLayers = () => {
      const selectedLayers = selectedLayersStore.value
      return selectedLayers
    }

    const createLayers = async (infos: Array<ScenaElementInfo>) => {
      try {
        const layers: ScenaElementLayer[] = []
        for (let index = 0; index < infos.length; ++index) {
          const { schema, meta } = infos[index]
          if (!schema || !meta) continue
          const type = schema.type
          const ItemType = schemaTypes[type]
          const view = new ViewElement<typeof ItemType | undefined>(undefined)
          const displayName = schema.displayName
          const id = getNextId()
          const title = `${displayName.toLocaleLowerCase()}-${id}`
          const { top = 100, left = 100 } = meta
          const props = await checkAttrs(
            schema.fields || {},
            { ...meta },
            {
              ItemType: true,
              view: true
            }
          )
          layers.push(
            createLayer({
              jsx: (
                <div class='moveable'>
                  <ViewNode class='moveable-node' forProps={{ ItemType, view, ...props }} />
                </div>
              ),
              schema,
              view,
              id,
              title,
              meta: {
                ...meta,
                position: 'absolute',
                left: left + 10,
                top: top + 10,
                layer_name: title
              }
            })
          )
        }
        if (layers.length > 0) {
          await setLayers([...editor.layers.layers, ...layers])
          setSelectedLayers(layers)
        }
      } catch (error) {
        //
      }
    }

    const clear = () => {
      clearStates()
      editor.clear()

      setLayers([])
      setSelectedLayers([])
    }

    editorRef.value = {
      state: editor,
      setLayers,
      removeLayers,
      removeSelectedLayers,
      setSelectedLayers,
      setProperty,
      setFrameProperties,
      setViewProperties,
      clear,
      getSelectedLayers,
      createLayers
    }
    useStoreValue($editor, editorRef.value)

    const onResize = () => {
      horizontalGuidesRef.value?.resize()
      verticalGuidesRef.value?.resize()
    }

    const onMenuChange = (id: string) => {
      editor.menu.selected = id
    }

    const onBlur = (e: any) => {
      // const target = e.target as HTMLElement | SVGElement
      // if (!checkInput(target)) {
      //   return
      // }
      // const parentTarget = getParnetScenaElement(target)
      // if (!parentTarget) {
      //   return
      // }
      // const info = editor.viewport.getInfoByElement(parentTarget)!
      // if (!info.attrs!.contenteditable) {
      //   return
      // }
      // const nextText = (parentTarget as HTMLElement).innerText
      // if (info.innerText === nextText) {
      //   return
      // }
      // editor.historys.addHistory('changeText', {
      //   id: info.id,
      //   prev: info.innerText,
      //   next: nextText
      // })
      // info.innerText = nextText
    }

    const [collect, connectDrop] = useDrop({
      accept: ItemTypes.CONTROL,
      drop: async (item: DragBoxItem, monitor) => {
        const viewport = viewportRef.value
        const type = item.type
        const name = item.name
        const values = (item.default && JSON.parse(JSON.stringify(item.default))) || {}
        const ItemType = schemaTypes[type]

        if (!viewport || !ItemType) {
          return
        }
        // 计算落点坐标
        const pointRect = viewport.getBoundingClientRect()
        let left = pointRect.left
        let top = pointRect.top
        const pointEnd = monitor.getSourceClientOffset()
        left = Math.floor((pointEnd!.x < left ? 0 : pointEnd!.x - left) / zoom.value)
        top = Math.floor((pointEnd!.y < top ? 0 : pointEnd!.y - top) / zoom.value)

        const { width = 200, height = 60 } = values
        const props = await checkAttrs(item.fields || {}, values, {
          ItemType: true,
          view: true
        })
        console.log(
          `%c=== 执行checkAttrs前的dsp控件default params：${JSON.stringify(
            values
          )} ===; $$$执行checkAttrs后的dsp控件default params：${JSON.stringify(props)} $$$`,
          "backgroundColor: '#fff';color: '#000';fontWeight: 'bold'"
        )
        const view = new ViewElement<typeof ItemType | undefined>(undefined)
        try {
          const displayName = item.displayName
          const id = getNextId()
          const title = `${displayName.toLocaleLowerCase()}-${id}`
          const layer = createLayer({
            jsx: (
              <div class='moveable'>
                <ViewNode class='moveable-node' forProps={{ ItemType, view, ...props }} />
              </div>
            ),
            meta: {
              obj_type: name,
              position: 'absolute',
              ...values,
              left,
              top,
              width,
              height,
              layer_name: title
            },
            id,
            schema: item,
            view,
            title
          })

          await setLayers([...editor.layers.layers, layer])
          setSelectedLayers([layer])
        } catch (error) {
          //
        }
      },
      collect: monitor => {
        const item = monitor.getItem()
        const isOver = monitor.isOver()
        const canDrop = monitor.canDrop()

        return {
          isOver,
          canDrop,
          item
        }
      }
    })
    const { isOver, item } = toRefs(collect)
    connectDrop(dropRef)

    onMounted(() => {
      editor.init()

      const onUpdate = () => {
        editor.events.trigger('update.rect', moveableRef.value?.getRect())
        requestAnimationFrame(() => {
          if (!moveableRef.value) {
            return
          }
          const rect = moveableRef.value.getRect()
          editor.actions.act('get.rect', {
            rect
          })
        })
      }

      editor.keywords.toggleState(['space'], $space, keyChecker)
      editor.actions.on('render.end', () => {
        if (!moveableRef.value) {
          return
        }

        const flatted = editor.layers.toFlatten(selectedLayersStore.value)
        const rect = moveableRef.value.getRect()
        if (flatted.length > 1) {
          const moveables = moveableRef.value.getManager().moveables || []
          moveables.forEach(child => {
            const target = child.state.target!
            const layer = target && editor.layers.getLayerByElement(target)
            if (layer) {
              const rect = child.getRect()
              setFrameProperties(layer, rect, false)
            }
          })
        } else if (flatted.length === 1) {
          const layer = flatted[0]
          setFrameProperties(layer, rect, false)
        }

        editor.events.trigger('update.rect', rect)
        editor.actions.act('get.rect', {
          rect
        })
      })
      editor.actions.on('changed.targets', onUpdate)
      editor.actions.on('update.rect', onUpdate)

      onResize()
      requestAnimationFrame(() => {
        infiniteViewerRef.value?.scrollCenter({ duration: 500, absolute: true })
      })

      editor.events.on('on-editor-loaded', () => {
        const { option } = editor.info || {}
        setCurrentScena(option ? { ...option } : undefined)
        setIsMove(editor.menu.isMove)
        showRightPanel.value = true
      })

      editor.events.on('on-editor-option', () => {
        const { option } = editor.info || {}
        setCurrentScena(option ? { ...option } : undefined)
      })

      editor.events.on('on-editor-update', param => {
        if (param.zoom !== undefined) {
          setZoom(editor.state.zoom)
        }
      })
      editor.menu.on('on-menu-update', param => {
        if (param.selected !== undefined) {
          setSelectedMenu(editor.menu.selected)
          setIsMove(editor.menu.isMove)

          if (editor.menu.isMove) {
            setSelectedLayers([])
            onResize()
          }
        }
      })
      window.addEventListener('resize', onResize)

      editor.actions.on('select.all', e => {
        e.inputEvent?.preventDefault()
        const layers = root.get($layers)

        const childs = editor.layers.selectSameDepthChilds(
          [],
          layers.map(layer => layer.root.current!),
          []
        )

        setSelectedLayers(editor.layers.toLayerGroups(childs))
      })
      editor.actions.on('on.save', e => {
        e.inputEvent?.preventDefault()
        onSave()
      })

      // register key
      editor.keywords.toggleState(['shift'], $shift, keyChecker)
      editor.keywords.toggleState(['space'], $space, keyChecker)
      editor.keywords.toggleState(['alt'], $alt, keyChecker)
      editor.keywords.actionDown(['left'], 'move.left')
      editor.keywords.actionDown(['right'], 'move.right')
      editor.keywords.actionDown(['up'], 'move.up')
      editor.keywords.actionDown(['down'], 'move.down')
      editor.keywords.actionUp(['delete'], 'remove.targets')
      editor.keywords.actionUp(['backspace'], 'remove.targets')

      editor.keywords.toggleState([isMacintosh ? 'meta' : 'ctrl'], $meta, keyChecker)
      editor.keywords.actionDown([isMacintosh ? 'meta' : 'ctrl', 'a'], 'select.all')
      editor.keywords.actionDown([isMacintosh ? 'meta' : 'ctrl', 's'], 'on.save')
      editor.keywords.actionDown([isMacintosh ? 'meta' : 'ctrl', 'z'], 'request.history.undo')
      editor.keywords.actionDown(
        [isMacintosh ? 'meta' : 'ctrl', 'shift', 'z'],
        'request.history.redo'
      )
      registerHistoryTypes(editor.historys)
    })

    onUnmounted(() => {
      window.removeEventListener('resize', onResize)
      clearStates()
      Editor.destroyEditor()
    })

    const onSave = async () => {
      console.log('onSave')
      console.log('layers', layers.value)
      console.log('editor', editor.info)
      console.log('option', editor.info?.option)
      const option = editor.info?.option
      const name = editor.info?.name

      const obj_vec: Array<UObject> = []
      const notValided: Array<ScenaElementLayer> = []
      layers.value.forEach(layer => {
        const schema = layer.schema
        // if (!schema?.fields || !checkValid(schema?.fields, layer.meta)) {
        //   notValided.push(layer)
        // } else {
        obj_vec.push(JSON.parse(JSON.stringify(layer.meta)))
        // }
      })
      // if (notValided.length > 0) {
      //   setSelectedLayers(notValided, true)

      //   WuiMessage({
      //     message: 'Not Valided',
      //     type: 'error'
      //   })
      //   return
      // }

      const result = {
        name,
        ...option,
        obj_vec
      }
      console.log('result', result)
      const res = await bizEngine.value?.modifyDisplay(fileIndex.value, result as UDisplay)
      if (res) {
        WuiMessage({
          message: 'Save Success',
          type: 'success',
          offset: 80
        })
      } else {
        WuiMessage({
          message: 'Save Failed',
          type: 'error',
          offset: 80
        })
      }
      console.log('-------- ', res)
    }

    return () => (
      <div class={prefix('editor', darkMode.value ? '' : 'light-mode')}>
        <TopView
          class={prefix('editor-top')}
          selected={selectedMenu.value}
          onSelect={onMenuChange}
          onSave={onSave}
        />
        <div class={prefix('editor-area')}>
          <PanelGroup direction='horizontal'>
            <Panel class='scena-panel-left' defaultSize={20}>
              <ComponentPanel />
            </Panel>
            <PanelResizeHandle class='scena-resize-handle' style={{ marginLeft: '3px' }} />
            <Panel defaultSize={58} class='scena-center'>
              <div class='scena-canvas' ref={dropRef}>
                <div
                  class={prefix('reset')}
                  onClick={() => {
                    infiniteViewerRef.value?.scrollCenter({ duration: 500, absolute: true })
                  }}></div>
                {(showGuides.value && (
                  <GuidesView
                    forRef={horizontalGuidesRef}
                    forProps={{
                      type: 'horizontal',
                      className: prefix('guides', 'horizontal')
                    }}
                  />
                )) || <></>}
                {(showGuides.value && (
                  <GuidesView
                    forRef={verticalGuidesRef}
                    forProps={{
                      type: 'vertical',
                      className: prefix('guides', 'vertical')
                    }}
                  />
                )) || <></>}
                {(currentScena.value && (
                  <>
                    <InfiniteView forRef={infiniteViewerRef}>
                      <Viewport
                        forRef={viewportRef}
                        forProps={{
                          onBlur
                        }}>
                        <MoveableView forRef={moveableRef} forProps={{ className: 'moveable' }} />
                      </Viewport>
                    </InfiniteView>
                    <SelectoView forRef={selectoRef} forProps={{ className: prefix('selecto') }} />
                  </>
                )) || <></>}
              </div>
            </Panel>
            <PanelResizeHandle class='scena-resize-handle' />

            <Panel
              v-show={showRightPanel.value}
              class='scena-panel-right'
              defaultSize={22}
              maxSize={50}>
              {currentScena.value && <PropertyPanel />}
            </Panel>
          </PanelGroup>
        </div>
      </div>
    )
  }
})
