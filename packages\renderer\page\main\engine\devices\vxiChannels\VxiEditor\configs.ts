import type { DialogConfig, NestedDialogContext } from '@/renderer/components/DynamicDIalog/types'

export const STATUSMATRIX: Omit<DialogConfig, 'form' | 'table'> = {
  title: (context?: NestedDialogContext) => {
    return `Status Matrix Editor  Engine:${context?.engineName}  EEC: ${context?.currentVxi?.eec}`
  },
  width: '500px',
  height: '700px',
  key: 'STATUSMATRIX',
  formTemplate: {
    columns: [
      {
        type: 'matrix',
        name: 'dis'
      },
      {
        type: 'matrix',
        name: 'bcd'
      },
      {
        type: 'matrix',
        name: 'bnr'
      }
    ]
  },
  defaultValues: {
    form: (context?: NestedDialogContext) => {
      return {
        dis: {
          normal: '2',
          matrixs: [
            { color: 'green', content: 'Not Used' },
            { color: 'green', content: 'Not Used' },
            { color: 'green', content: 'Not Used' },
            { color: 'green', content: 'Not Used' }
          ],
          lable: 'dis'
        },
        bcd: {
          normal: '1',
          matrixs: [
            { color: 'green', content: 'Not Used' },
            { color: 'green', content: 'Not Used' },
            { color: 'green', content: 'Not Used' },
            { color: 'green', content: 'Not Used' }
          ],
          lable: 'bcd'
        },
        bnr: {
          normal: '0',
          matrixs: [
            { color: 'green', content: 'Not Used' },
            { color: 'green', content: 'Not Used' },
            { color: 'green', content: 'Not Used' },
            { color: 'green', content: 'Not Used' }
          ],
          lable: 'bnr'
        }
      }
    }
  }
}

export const DISCRETELABELCOLORS: Omit<DialogConfig, 'form' | 'table'> = {
  title: (context?: NestedDialogContext) => {
    return `ARINC Discrete Labels ON/OFF Colors`
  },
  height: '200',
  width: '400',
  key: 'DISCRETELABELCOLORS',
  formTemplate: {
    columns: [
      {
        label: 'ON Color',
        name: 'on_color',
        type: 'colorSelect',
        labelWidth: '100'
      },
      {
        label: 'OFF Color',
        name: 'off_color',
        type: 'colorSelect',
        labelWidth: '100'
      }
    ]
  },
  defaultValues: {
    form: (context?: NestedDialogContext) => {
      console.log('DISCRETELABELCOLORS', context)
      return {
        on_color: '',
        off_color: ''
      }
    }
  }
}

export const VXIEDITCHANNEL: Omit<DialogConfig, 'form' | 'table'> = {
  title: (context?: NestedDialogContext) => {
    return `ARINC Channel Editor - Transmitter ${context?.currentChannel?.channels || ''}`
  },
  key: 'VXIEDITCHANNEL',
  tableTemplate: {
    columns: [
      {
        label: 'Label',
        name: 'label',
        type: 'input'
      },
      {
        label: 'Signal Name',
        name: 'signal_name',
        type: 'input'
      },
      {
        label: 'Data Type',
        name: 'data_type',
        type: 'select',
        options: [
          { label: 'BNR', value: 'BNR' },
          { label: 'BCD', value: 'BCD' },
          { label: 'DIS', value: 'DIS' },
          { label: 'ISO', value: 'ISO' },
          { label: 'ANG', value: 'ANG' },
          { label: 'HYB-BNR', value: 'HYB-BNR' },
          { label: 'HYB-BCD', value: 'HYB-BCD' },
          { label: 'HYB-DIS', value: 'HYB-DIS' },
          { label: 'HYB-ISO', value: 'HYB-ISO' },
          { label: 'HYB-ANG', value: 'HYB-ANG' }
        ]
      },
      {
        label: 'Active',
        name: 'active',
        type: 'select',
        options: [
          { label: 'INACTIVE', value: 'INACTIVE' },
          { label: 'ACTIVE', value: 'ACTIVE' }
        ]
      }
    ]
  },
  defaultValues: {
    table: (context?: NestedDialogContext) => {
      return []
    }
  },
  nestedDialogs: {
    editSignal: {
      title: (context?: NestedDialogContext) => {
        return `${context?.deviceData?.name} ARINC Transmitter Signal Editor`
      },
      width: '700px',
      key: 'VXICHANNELSIGNAL',
      formTemplate: {
        columns: [
          {
            type: 'span',
            name: 'signal',
            label: 'Signal',
            labelWidth: '50'
          },
          {
            type: 'span',
            name: 'channel',
            label: 'Channel',
            labelWidth: '50'
          },
          {
            type: 'span',
            name: 'label',
            label: 'Label',
            labelWidth: '50'
          },
          {
            type: 'span',
            name: 'type',
            label: 'Type',
            labelWidth: '50'
          },
          {
            type: 'select',
            label: 'SDI',
            name: 'sdi',
            options: [
              { label: '00', value: '00' },
              { label: '01', value: '01' },
              { label: '10', value: '10' },
              { label: '11', value: '11' },
              { label: 'NO_DSI', value: 'no_sdi' }
            ]
          },
          {
            type: 'select',
            label: 'Modify',
            name: 'modify',
            options: [
              { label: 'OFF', value: 'off' },
              { label: 'ON', value: 'on' }
            ]
          },
          {
            type: 'select',
            label: 'Modify',
            name: 'modify',
            options: [
              { label: 'OFF', value: 'off' },
              { label: 'ON', value: 'on' }
            ]
          },
          {
            type: 'input',
            name: 'rate',
            label: 'Rate(ms)'
          },
          {
            type: 'select',
            label: 'SSM',
            name: 'ssm',
            options: [
              { label: '00', value: '00' },
              { label: '01', value: '01' },
              { label: '10', value: '10' },
              { label: '11', value: '11' }
            ]
          },
          {
            type: 'input',
            name: 'value',
            label: 'Value'
          },
          {
            type: 'input',
            name: 'binary_range',
            label: 'Binary Range'
          },
          {
            type: 'input',
            name: 'significant_bits',
            label: 'Significant Bits'
          },
          {
            type: 'range',
            label: 'Signal Range',
            name: 'signal_range',
            labelWidth: '100',
            class: 'full-width range-form-item'
          },
          {
            type: 'input',
            name: 'comment',
            label: 'Comment',
            isArea: true
          }
        ]
      },
      defaultValues: {
        form: (context?: NestedDialogContext) => {
          return {
            signal: '22',
            channel: 'A',
            label: 'Signal Label',
            type: 'BNR',
            sdi: 'no_sdi',
            modify: 'off',
            rate: '100',
            ssm: '00',
            value: '0',
            binary_range: '0',
            significant_bits: '0',
            signal_range: {
              minimum: '0',
              maximum: '100',
              units: '222'
            },
            comment: '21312312'
          }
        }
      }
    }
  }
}

export const DEVICE_ENG_CONFIGS = {
  STATUSMATRIX: STATUSMATRIX,
  DISCRETELABELCOLORS: DISCRETELABELCOLORS,
  VXIEDITCHANNEL: VXIEDITCHANNEL
} as const
