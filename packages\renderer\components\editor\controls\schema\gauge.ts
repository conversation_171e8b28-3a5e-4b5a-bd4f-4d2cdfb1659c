import { baseConfig, BaseConfigType, baseDefault } from '../common'
import { ColorConfigType, SelectConfigType, SliderConfigType } from '../types'
import { DspGaugeType, DspGaugeTypes, DspGaugeParamBox, DspGaugeNumsOutSide } from '@wuk/wui'

export type DspGaugeConfigType =
  | BaseConfigType
  | ColorConfigType
  | SliderConfigType
  | SelectConfigType<number | (typeof DspGaugeTypes)[number]>

// param_id: string
// gauge_type: string
// peak_param_id: string
// label_color: string
// needle_color: string
// nums_out_side: number
// tic_font: number
// format: string
// max: number
// min: number
// label: string
// units: string
// label_inter: number
// tic_inter: number
// radius: number
// param_box: number
// param_box_color: string
// shading: number
// tic_font_size: number
// axis_line_width: number
// axis_line_space: number
// unit_font_size: number
// digit_radius: string
// digit_font_weight: number
// digit_font_size: number
// unit_font_weight: number
// label_font_size: number
// label_font_weight: number
// gauge_color: string // todo: cfg-reader待接入
// track_color: string // todo: cfg-reader待接入
// inner_ring_color: string // todo: cfg-reader待接入

export const dspGaugeConfig: Array<DspGaugeConfigType> = [
  ...baseConfig,
  {
    key: 'label_font_size',
    name: 'Label Font Size',
    type: 'Number',
    unit: 'px',
    range: [5, 100],
    field: 'labelFontSize',
    comments: '头部label 字体大小'
  },
  {
    key: 'label_font_weight',
    name: 'label Font Weight',
    type: 'Number',
    field: 'labelFontWeight',
    range: [100, 900],
    step: 100,
    comments: 'label字体的粗细程度'
  },
  {
    key: 'digit_font_size',
    name: 'Digit Font Size',
    type: 'Number',
    unit: 'px',
    range: [5, 100],
    field: 'digitFontSize',
    comments: 'digit字体大小'
  },
  {
    key: 'digit_radius',
    name: 'Digit Radius',
    type: 'Number',
    unit: 'px',
    field: 'digitRadius',
    range: [0, 10],
    cast: 'string',
    comments: 'Digit box的圆角弧度'
  },
  {
    key: 'digit_font_weight',
    name: 'Digit Font Weight',
    type: 'Number',
    field: 'digitFontWeight',
    range: [100, 900],
    step: 100,
    comments: 'digit字体的粗细程度'
  },
  {
    key: 'unit_font_weight',
    name: 'Unit Font Weight',
    type: 'Number',
    field: 'unitFontWeight',
    range: [100, 900],
    step: 100,
    comments: 'unit字体的粗细程度'
  },
  {
    key: 'gauge_type',
    name: 'Gauge Type',
    type: 'Select',
    field: 'gaugeType',
    comments: '仪表盘显示的类型',
    range: [
      { key: DspGaugeType.DIAL_NEEDLE, text: 'DIAL_NEEDLE' },
      { key: DspGaugeType.METER_RIGHT, text: 'METER_RIGHT' },
      { key: DspGaugeType.METER_UP, text: 'METER_UP' }
    ]
  },
  {
    key: 'param_id',
    name: 'Param ID',
    type: 'Param',
    comments: '参数ID'
  },
  {
    key: 'peak_param_id',
    name: 'Peak Param ID',
    type: 'Param',
    field: 'peakParamId',
    comments: '峰值参数ID'
  },
  {
    key: 'label_color',
    name: 'Label Color',
    type: 'Color',
    field: 'labelColor',
    comments: '头部标签颜色'
  },
  {
    key: 'needle_color',
    name: 'Needle Color',
    type: 'Color',
    field: 'needleColor',
    comments: '仪表盘指针颜色'
  },
  {
    key: 'nums_out_side',
    name: 'Nums Position',
    type: 'Select',
    field: 'numsOutSide',
    comments: '仪表盘刻度显示的位置',
    range: [
      { key: DspGaugeNumsOutSide.INSIDE, text: 'Inside' },
      { key: DspGaugeNumsOutSide.OUTSIDE, text: 'Outside' }
    ]
  },
  {
    key: 'format',
    name: 'Format',
    type: 'Number',
    field: 'format',
    comments: '数值格式化'
  },
  {
    key: 'max',
    name: 'Max Value',
    type: 'Number',
    field: 'max',
    comments: '仪表盘刻度最大值'
  },
  {
    key: 'min',
    name: 'Min Value',
    type: 'Number',
    field: 'min',
    comments: '仪表盘刻度最小值'
  },
  {
    key: 'label',
    name: 'Label',
    type: 'Text',
    field: 'label',
    comments: '头部描述label值'
  },

  {
    key: 'units',
    name: 'Unit',
    type: 'Text',
    field: 'unit',
    comments: '单位'
  },
  {
    key: 'label_inter',
    name: 'Label Interval',
    type: 'Number',
    unit: 'px',
    field: 'labelInter',
    range: [1, 10],
    comments: '刻度值间隔'
  },

  {
    key: 'radius',
    name: 'Radius',
    field: 'radius',
    type: 'Number',
    unit: 'px',
    range: [50, 500],
    comments: '仪表盘半径'
  },

  {
    key: 'param_box',
    name: 'Param Box',
    type: 'Select',
    field: 'paramBox',
    comments: '参数框样式',
    range: [
      { key: DspGaugeParamBox.NONE, text: 'None' },
      { key: DspGaugeParamBox.OUTLINE, text: 'OutLine' },
      { key: DspGaugeParamBox.FILLED, text: 'Filled' }
    ]
  },
  {
    key: 'param_box_color',
    name: 'Param Box Color',
    type: 'Color',
    field: 'paramBoxColor',
    comments: '参数框颜色'
  },
  {
    key: 'shading',
    name: 'Shading',
    type: 'Number',
    unit: 'px',
    field: 'shading',
    range: [0, 10],
    comments: '边框宽度'
  },

  {
    key: 'tic_font_size',
    name: 'Tick Font Size',
    type: 'Number',
    unit: 'px',
    field: 'ticFontSize',
    range: [5, 100],
    comments: '底部刻度字体大小'
  },
  {
    key: 'tic_inter',
    name: 'Tick Interval',
    type: 'Number',
    field: 'ticInter',
    range: [10, 1000],
    comments: '刻度值增量'
  },

  {
    key: 'axis_line_width',
    name: 'Axis Line Width',
    type: 'Number',
    unit: 'px',
    field: 'axisLineWidth',
    range: [0, 50],
    comments: '仪表盘中间盘线宽度'
  },
  {
    key: 'axis_line_space',
    name: 'Axis Line Space',
    type: 'Number',
    unit: 'px',
    field: 'axisLineSpace',
    range: [0, 18],
    comments: '仪表盘线之间的间距'
  },
  {
    key: 'unit_font_size',
    name: 'Unit Font Size',
    type: 'Number',
    unit: 'px',
    field: 'unitFontSize',
    range: [5, 100],
    comments: '单位字体大小'
  },
  {
    key: 'gauge_color',
    name: 'Gauge Color',
    type: 'Color',
    field: 'gaugeColor',
    comments: '仪表盘背景色'
  },
  {
    key: 'track_color',
    name: 'Track Color',
    type: 'Color',
    field: 'trackColor',
    comments: '仪表盘表盘轨道背景色'
  },
  {
    key: 'inner_ring_color',
    name: 'Inner Ring Color',
    type: 'Color',
    field: 'innerRingColor',
    comments: '仪表盘内圈颜色'
  }
]

export const dspGaugeDefault: Record<string, any> = {
  ...baseDefault,
  digit_font_size: 23,
  digit_radius: '5',
  digit_font_weight: 400,
  unit_font_weight: 400,
  label_font_size: 23,
  label_font_weight: 400,
  param_id: 'None',
  peak_param_id: 'None',
  width: 300,
  height: 400,
  gauge_type: DspGaugeType.DIAL_NEEDLE,
  needle_color: 'Black',
  gauge_color: 'Clear',
  track_color: 'Green',
  inner_ring_color: 'Black',
  nums_out_side: DspGaugeNumsOutSide.OUTSIDE,
  format: '8.2',
  max: 500,
  min: 0,
  label: 'Label',
  label_color: 'Black',
  units: 'Desc',
  unit_font_size: 23,
  label_inter: 2,
  tic_inter: 100,
  radius: 120,
  param_box: DspGaugeParamBox.NONE,
  param_box_color: 'Black',
  shading: 0,
  tic_font_size: 23,
  axis_line_width: 20,
  axis_line_space: 10,
  digit_font_fize: 23
}
