import { h, nextTick } from 'vue'
import { kEditresDefault } from '../consts'
import {
  DisplayScenaInfo,
  EditorInstance,
  ScenaEditorState,
  ScenaElementLayer,
  ScenaInfo,
  ViewElement,
  ViewRef
} from '../types'
import Moveable from 'vue3-moveable'
import Actions from './actions'
import Clipboard from './clipboad'
import EventBus from './eventbus'
import Historys from './historys'
import Keywords from './keywords'
import Memory from './memory'

import { StoreRootValue } from '@/renderer/store'
import { DisplayObjectType, UDisplay, UObject } from '@wuk/cfg'
import { checkAttrs, components, schemaTypes } from '../controls'
import { HandIcon, Icon, MoveToolIcon } from '../icon'
import { clearStates } from '../stores'
import Debugger from '../utils/debugger'
import { ViewNode } from '../view'
import Layers, { createLayer, getNextId } from './layers'
import { BizMain } from '@/renderer/logic'

export class Menu {
  private static readonly MENUS: Array<typeof Icon> = [
    MoveToolIcon,
    HandIcon
    // TextIcon,
    // CropIcon,
    // RectIcon,
    // RoundRectIcon,
    // OvalIcon
  ]

  private _selected: string
  private _icons: Map<string, Icon>

  constructor(private readonly editor: Editor) {
    this._selected = MoveToolIcon.id
    this._icons = new Map()

    Menu.MENUS.map(Cls => this._icons.set(Cls.id, new Cls(editor)))
  }

  init() {}

  get selected() {
    return this._selected
  }

  set selected(val: string) {
    if (val !== this._selected) {
      this._selected = val

      this.forceUpdate(undefined, { selected: this._selected })
    }
  }

  get isMove() {
    return this._selected === HandIcon.id
  }

  get icons() {
    return Menu.MENUS.map(Cls => this._icons.get(Cls.id)!)
  }

  get maker() {
    return Menu.MENUS.filter(m => m.id === this._selected)[0]
  }

  blur() {
    this._icons.forEach((icon, key) => icon.blur())
  }

  public on(eventName: string, handler: (...args: any[]) => void) {
    this.editor.events.on(eventName, handler)
  }

  public off(eventName: string, handler: (...args: any[]) => void) {
    this.editor.events.off(eventName, handler)
  }

  private async forceUpdate<T>(callback?: () => any, state?: Partial<T>) {
    this.editor.events.trigger('on-menu-update', state)

    await nextTick(() => {
      callback?.()
    })
  }
}

export class Editor {
  private static _impl?: Editor

  private _state: ScenaEditorState
  private _historys: Historys
  private _events: EventBus
  private _memory: Memory
  private _keywords: Keywords
  private _clipboard: Clipboard
  private _actions: Actions
  private _menu: Menu

  private _layers: Layers

  private _info?: DisplayScenaInfo

  private _moveable: Moveable | undefined

  constructor(
    private readonly root: StoreRootValue,
    readonly mgr: ViewRef<EditorInstance | undefined>
  ) {
    this._actions = new Actions()
    this._historys = new Historys(mgr, this._actions)
    this._events = new EventBus()
    this._memory = new Memory()

    this._keywords = new Keywords(root, this._actions)
    this._clipboard = new Clipboard(this)
    this._menu = new Menu(this)

    this._layers = new Layers()

    this._state = {
      zoom: 1,
      showComponentPanel: true,
      showControlsPanel: true,
      showLayersPanel: true,
      showOptionsPanel: true,
      showPropertiesPanel: true,
      showHistoryPanel: true,
      showRightPanel: false
    }
  }

  static createEditor(root: StoreRootValue, mgr: ViewRef<EditorInstance | undefined>) {
    return Editor._impl || (Editor._impl = new Editor(root, mgr))
  }

  static destroyEditor() {
    Editor._impl?.destory()
    Editor._impl = undefined
  }

  static clearEditor() {
    clearStates()
    Editor._impl?.clear()
  }

  static get impl() {
    if (Editor._impl === undefined) {
      throw new Error('Editor need created before')
    }
    return Editor._impl!
  }

  get state() {
    return this._state
  }
  get historys() {
    return this._historys
  }
  get console() {
    return Debugger
  }
  get events() {
    return this._events
  }
  get memory() {
    return this._memory
  }
  get keywords() {
    return this._keywords
  }
  get clipboard() {
    return this._clipboard
  }
  get actions() {
    return this._actions
  }

  get menu() {
    return this._menu
  }

  get moveable() {
    return this._moveable
  }

  set moveable(val: Moveable | undefined) {
    this._moveable = val
  }

  get info() {
    return this._info
  }

  get layers() {
    return this._layers
  }

  init() {
    this._memory.set('background-color', '#4af')
    this._memory.set('color', '#333')

    this._actions.on('move.left', e => {
      this.move(-1, 0)
      e.inputEvent?.preventDefault()
    })
    this._actions.on('move.right', e => {
      this.move(1, 0)
      e.inputEvent?.preventDefault()
    })
    this._actions.on('move.up', e => {
      this.move(0, -1)
      e.inputEvent?.preventDefault()
    })
    this._actions.on('move.down', e => {
      this.move(0, 1)
      e.inputEvent?.preventDefault()
    })
    this._actions.on('remove.targets', e => {
      this.mgr.current?.removeSelectedLayers()
    })

    this._actions.on('request.history.undo', e => {
      this._historys.undo()
      e.inputEvent?.preventDefault()
    })
    this._actions.on('request.history.redo', e => {
      this._historys.redo()
      e.inputEvent?.preventDefault()
    })
  }

  destory() {
    this._events.off()
    this._memory.clear()
    this._keywords.destroy()
    this._clipboard.destroy()
    this._actions.destroy()
  }

  clear() {
    this._info = undefined
    this._memory.clear()
    this._historys.clear()
    this._layers.clear()
    this._actions.clear()
  }

  loadDisplay(index: number, name: string, file: string, meta: UDisplay) {
    if (this._info?.name !== name) {
      this.mgr.current?.clear()

      const option = this.loadOptions(index, name, file, meta)
      this._info = { name, option, meta }
      this._events.trigger('on-editor-loaded')
      nextTick(() => this.loadDisplayList(meta))
    }
  }

  changeOption({ editres, background }: Partial<ScenaInfo>) {
    const option = this._info?.option
    const meta = this._info?.meta
    if (!option) return
    if (background && meta) {
      meta.background = background || ''
      background !== undefined && (option.background = background)
    }
    if (editres !== undefined && meta) {
      const { width = 1280, height = 1024 } = BizMain.impl?.resolutions.forData(editres) || {}
      option.editres = editres
      option.width = width
      option.height = height
      meta.editres = editres
    }

    this._events.trigger('on-editor-option')
  }

  changeComponent(index: number, object: UObject) {
    if (!this._info) return
    this._info.meta.obj_vec[index] = {
      ...this._info!.meta.obj_vec[index],
      ...object
    }
    this._events.trigger('on-editor-property', { index })
  }

  promiseState(state: Partial<ScenaEditorState>) {
    return new Promise((resolve: any) => {
      this.setState(state, () => {
        resolve()
      })
    })
  }

  setState(state: Partial<ScenaEditorState>, callback?: () => any) {
    this._state = {
      ...this._state,
      ...state
    }
    this.forceUpdate<ScenaEditorState>(() => {
      callback?.()
    }, state)
  }

  selectMenu(menu: string) {
    this._menu.selected = menu
  }

  private loadOptions(
    index: number,
    name: string,
    file: string,
    { editres = kEditresDefault, background }: UDisplay
  ): ScenaInfo {
    const { width = 1280, height = 1024 } = BizMain.impl?.resolutions.forData(editres) || {}
    return { index, name, file, editres, background, width, height }
  }

  private async createViewLayer(item: UObject) {
    const component = components[item.obj_type as DisplayObjectType]
    if (!component) {
      console.info('Component not finded', item.obj_type)
      return undefined
    }
    const type = component.type
    const ItemType = schemaTypes[type]
    const view = new ViewElement<typeof ItemType | undefined>(undefined)
    const displayName = component.displayName
    const id = getNextId()
    const title = `${displayName.toLocaleLowerCase()}-${id}`
    const props = await checkAttrs(
      component.fields || {},
      { ...item },
      {
        ItemType: true,
        view: true
      }
    )
    return createLayer({
      jsx: (
        <div class='moveable'>
          <ViewNode class='moveable-node' forProps={{ ItemType, view, ...props }} />
        </div>
      ),
      schema: component,
      view,
      id,
      title,
      meta: {
        position: 'absolute',
        ...item,
        layer_name: title
      }
    })
  }

  private async loadDisplayList({ obj_vec = [] }: UDisplay) {
    const layers: ScenaElementLayer[] = []
    for (let index = 0; index < obj_vec.length; ++index) {
      const item = obj_vec[index]
      const layer = await this.createViewLayer(item)
      layer && layers.push(layer)
    }

    this.mgr.value?.setLayers(layers, undefined, undefined, true)
  }

  private async forceUpdate<T>(callback?: () => any, param?: Partial<T>) {
    this._events.trigger('on-editor-update', param)

    await nextTick(() => {
      callback?.()
    })
  }

  private move(deltaX: number, deltaY: number) {
    this._moveable?.request('draggable', { deltaX, deltaY }, true)
  }
}
