import { baseConfig, BaseConfigType, baseDefault } from '../common'
import { ColorConfigType, SliderConfigType } from '../types'

export type DspLineConfigType = BaseConfigType | ColorConfigType | SliderConfigType

// line_width: number
// line_color: string
// shading: number
export const dspLineConfig: Array<DspLineConfigType> = [
  ...baseConfig,
  {
    key: 'shading',
    name: 'Shading',
    type: 'Number',
    unit: 'px',
    field: 'shading',
    range: [-10, 10],
    comments: '边框阴影宽度'
  },
  {
    key: 'line_width',
    name: 'Line Width',
    type: 'Number',
    unit: 'px',
    field: 'lineWidth',
    range: [1, 10],
    comments: '线条粗细'
  },
  {
    key: 'line_color',
    name: 'Line Color',
    type: 'Color',
    field: 'lineColor',
    comments: '线条颜色'
  }
]
export const dspLineDefault: Record<string, any> = {
  ...baseDefault,
  width: 200,
  height: 5,
  shading: 0,
  line_width: 5,
  line_color: 'LightGray'
}
