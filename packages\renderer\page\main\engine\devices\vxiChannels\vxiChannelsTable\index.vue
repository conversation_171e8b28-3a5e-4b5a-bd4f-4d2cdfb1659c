<template>
  <div :class="[b(), 'cfg-setup']">
    <div :class="['cfg-setup_table']">
      <wui-table show-overflow-tooltip border :data="model.list" @row-click="handleRowClick">
        <wui-table-column label="Device Name" align="center" show-overflow-tooltip>
          <template #default="{ row }">
            <span>{{ row.deviceName }}</span>
          </template>
        </wui-table-column>
        <wui-table-column label="Slot Num" align="center" show-overflow-tooltip>
          <template #default="{ row }">
            <span>{{ row.slotNum }}</span>
          </template>
        </wui-table-column>
        <wui-table-column label="Addr Num" align="center" show-overflow-tooltip>
          <template #default="{ row }">
            <span>{{ row.addrNum }}</span>
          </template>
        </wui-table-column>
      </wui-table>
    </div>

    <VxiEditor v-model="showVxiEditor" :device-data="selectedDevice" />
  </div>
</template>

<script lang="ts" setup>
import { ref, inject, watchEffect, reactive, toRef } from 'vue'
import $styles from './index.module.scss'
import { useBem } from '@/renderer/hooks'
import VxiEditor from '../VxiEditor/index.vue'

import { contextKey, device, VxiChannelData } from '../consts'

const { b } = useBem('vxi-channels-table', $styles)

const vxiContext = inject(contextKey)

const model = reactive<{ list: device[] }>({
  list: []
})

// VXI编辑器相关状态
const showVxiEditor = ref(false)
const selectedDevice = ref<
  | {
      name: string
      slot: number
      address: number
      type: string
    }
  | undefined
>(undefined)

const handleRowClick = (row: device) => {
  openDeviceEditor(row)
}

// 打开设备编辑器
const openDeviceEditor = (row: device) => {
  selectedDevice.value = {
    name: row.deviceName || 'CONDOR_429',
    slot: row.slotNum || 18,
    address: row.addrNum || 123,
    type: row.deviceName || 'CONDOR_429'
  }
  showVxiEditor.value = true
}

// 保存设备数据
const handleSaveDevice = (deviceData: any) => {
  console.log('Save device data:', deviceData)
  // 这里可以调用API保存数据
}

watchEffect(() => {
  const { originData = {} as VxiChannelData } = vxiContext?.curEditVxiInfo || {}
  model.list =
    originData.deviceList?.map(item => ({
      ...item,
      name: originData.name
    })) || []
})
</script>
