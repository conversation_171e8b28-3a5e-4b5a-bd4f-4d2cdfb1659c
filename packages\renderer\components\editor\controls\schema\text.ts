import { DspTextAlign, DspTextDir } from '@wuk/wui'
import { baseConfig, BaseConfigType, baseDefault } from '../common'
import {
  ColorConfigType,
  FontConfigType,
  MutiTextConfigType,
  ParamConfigType,
  SelectConfigType,
  SliderConfigType,
  TableConfigType
} from '../types'
export type DspTextConfigType =
  | BaseConfigType
  | SelectConfigType<number>
  | ColorConfigType
  | FontConfigType
  | MutiTextConfigType
  | SliderConfigType
  | ParamConfigType

// direction: number
// alignment: number
// color: string
// value: string
// font_size: number
// font_weight: number
// item_vec: string[]
export const dspTextConfig: Array<DspTextConfigType | TableConfigType<DspTextConfigType>> = [
  ...baseConfig,
  {
    key: 'direction',
    name: 'Direction',
    type: 'Select',
    range: [
      { key: DspTextDir.VERTICAL, text: 'Vertical' },
      { key: DspTextDir.HORIZONTAL, text: 'Horizontal' }
    ],
    comments: '控件文本方向'
  },
  {
    key: 'alignment',
    name: 'Alignment',
    type: 'Select',
    range: [
      { key: DspTextAlign.LEFT, text: 'Align Left' },
      { key: DspTextAlign.CENTER, text: 'Align Center' },
      { key: DspTextAlign.RIGHT, text: 'Align Right' }
    ],
    comments: '文本对齐方式 - 默认左对齐'
  },
  {
    key: 'color',
    name: 'Color',
    type: 'Color',
    comments: '文本颜色'
  },
  {
    key: 'value',
    name: 'Value',
    type: 'Text',
    comments: '文本内容'
  },
  {
    key: 'font_size',
    name: 'Font Size',
    type: 'Number',
    unit: 'px',
    field: 'fontSize',
    range: [5, 100],
    comments: '文本大小'
  },
  {
    key: 'font_weight',
    name: 'Font Weight',
    type: 'Number',
    field: 'fontWeight',
    range: [100, 900],
    step: 100,
    comments: '文本加粗范围'
  }
]
export const dspTextDefault: Record<string, any> = {
  ...baseDefault,
  width: 100,
  height: 30,
  direction: DspTextDir.HORIZONTAL,
  alignment: DspTextAlign.LEFT,
  color: 'Black',
  value: 'Text',
  font_size: 23,
  font_weight: 400
}
