import { defineComponent, useModel } from 'vue'
import { useBem } from '@/renderer/hooks'
import $styles from './index.module.scss'
import ColorSelect from '@/renderer/components/ColorSelect'

export interface MatrixFormData {
  normal: string
  matrixs: matrix[]
  lable: string
}

export interface matrix {
  color: string
  content: string
}

export default defineComponent({
  name: 'Matrix',
  props: {
    modelValue: {
      type: Object as () => MatrixFormData,
      default: () => ({
        normal: '',
        matrixs: [],
        lable: ''
      })
    }
  },
  emits: ['update:modelValue'],
  setup(props) {
    const { b, e } = useBem('matrix', $styles)
    const modelValue = useModel(props, 'modelValue')

    const normalOptions = [
      {
        label: '0',
        value: '0'
      },
      {
        label: '1',
        value: '1'
      },
      {
        label: '2',
        value: '2'
      },
      {
        label: '3',
        value: '4'
      }
    ]

    const renderColorItem = (index: number, value: matrix) => {
      return (
        <div class={e('content', 'item')}>
          <span>{index}</span>
          <ColorSelect v-model={value.color} />
          <wui-input v-model={value.content} placeholder='Not Uesed' />
        </div>
      )
    }

    return () => (
      <div class={b()}>
        <div class={e('header')}>
          <div class={e('header', 'label')}>{modelValue.value.lable}</div>
          <div>
            <span>Normal: </span>
            <wui-select v-model={modelValue.value.normal}>
              {normalOptions.map(item => (
                <wui-option key={item.value} label={item.label} value={item.value} />
              ))}
            </wui-select>
          </div>
        </div>
        <div class={e('content')}>
          {modelValue.value.matrixs.map((item, index) => renderColorItem(index, item))}
        </div>
      </div>
    )
  }
})
