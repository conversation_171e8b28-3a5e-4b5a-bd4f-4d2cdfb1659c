import { baseConfig, BaseConfigType, baseDefault } from '../common'
import { DspFuncButtonType } from '@wuk/wui'
import { ColorConfigType, SelectConfigType, SliderConfigType } from '../types'

export type DspFuncButtonConfigType =
  | BaseConfigType
  | ColorConfigType
  | SliderConfigType
  | SelectConfigType<number>

// type: string
// label: string
// off_label: string   //constructor端能设置即可
// background_color: string
// text_color: string
// radius: string
// font_size: number
// font_weight: number
// crt_name: string
// quad_index: number
// display_name: string
export const dspFuncButtonConfig: Array<
  DspFuncButtonConfigType | SelectConfigType<string, DspFuncButtonConfigType>
> = [
  ...baseConfig,
  {
    key: 'type',
    name: 'Button Type',
    type: 'Select',
    field: 'type',
    comments: '按钮类型',
    range: [
      { key: DspFuncButtonType.CHANGEDSP, text: 'Change Display' },
      { key: DspFuncButtonType.CLOSE_CRT, text: 'Close CRT' },
      { key: DspFuncButtonType.COLLECT, text: 'Collect' },
      { key: DspFuncButtonType.EXIT, text: 'Exit' },
      { key: DspFuncButtonType.PARAM_LIST, text: 'Param List' },
      { key: DspFuncButtonType.PLOT_ACTIVE, text: 'Plot Active' },
      { key: DspFuncButtonType.PRINT_POINT, text: 'Print Point' },
      { key: DspFuncButtonType.REFRESH_PLOT, text: 'Refresh Plot' }
    ]
  },
  {
    key: 'label',
    name: 'Label',
    type: 'Text',
    field: 'label',
    comments: '按钮内部文本'
  },
  {
    key: 'off_label',
    name: 'Off Label',
    type: 'Text',
    field: 'offLabel',
    comments: '按钮关闭状态文本'
  },
  {
    key: 'background_color',
    name: 'Background Color',
    type: 'Color',
    field: 'boxColor',
    comments: '按钮背景颜色'
  },
  {
    key: 'text_color',
    name: 'Text Color',
    type: 'Color',
    field: 'labelColor',
    comments: '按钮文本颜色'
  },
  {
    key: 'radius',
    name: 'Button Radius',
    type: 'Number',
    unit: 'px',
    field: 'buttonRadius',
    cast: 'string',
    range: [0, 100],
    comments: '按钮的圆角半径'
  },
  {
    key: 'font_size',
    name: 'Label Font Size',
    field: 'labelFontSize',
    type: 'Number',
    unit: 'px',
    range: [5, 100],
    comments: '按钮内部文本字体大小'
  },
  {
    key: 'font_weight',
    name: 'Label Font Weight',
    field: 'labelFontWeight',
    type: 'Number',
    unit: 'px',
    range: [100, 900],
    step: 100,
    comments: '按钮内部文本字体粗细'
  },
  {
    key: 'crt_name',
    name: 'CRT Name',
    type: 'Text',
    field: 'crtName',
    comments: 'CRT名称'
  },
  {
    key: 'quad_index',
    name: 'Quad Index',
    type: 'Number',
    comments: '切换DSP时的象限索引',
    field: 'quadIndex'
  },
  {
    key: 'display_name',
    name: 'Display Name',
    type: 'Text',
    field: 'displayName',
    comments: 'dsp 名称'
  }
]

export const dspFuncButtonDefault: Record<string, any> = {
  ...baseDefault,
  width: 200,
  height: 60,
  radius: '5',
  font_size: 23,
  font_weight: 400,
  text_color: 'Black',
  background_color: 'Green',
  label: 'Label',
  type: DspFuncButtonType.CHANGEDSP
}
